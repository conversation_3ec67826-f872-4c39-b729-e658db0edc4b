# 船只地理坐标管理功能完善设计方案

## 1. 项目背景

### 1.1 需求概述
在当前船舶数据中心系统中，需要实现船只地理坐标的实时管理功能，特别是**台风预警**相关的地理计算：
- 获取所有船只的实时地理坐标（经纬度）
- 将船只坐标数据存储到Redis中，用于快速查询和距离计算
- **重点：实现船只与台风的距离计算和预警功能**
- 支持台风影响范围内船只的快速查询

### 1.2 当前系统架构分析
**现有架构：**
- **数据存储层**：MySQL（主数据库）+ HBase（时序数据）+ Redis（缓存层）
- **服务层**：Spring Boot微服务架构（snct-manage、snct-visual等模块）
- **数据流**：Kafka消息队列 → StoreService → HBase存储
- **GPS处理**：GpsService、RealtimeService等服务
- **台风系统**：TyphoonService、台风数据定时获取和Redis缓存

**现有问题：**
- StoreService只存储到HBase，未同步更新Redis
- 台风预警需要遍历计算所有船只与台风的距离，性能较低
- 缺乏高效的地理范围查询能力

## 2. 系统架构设计

### 2.1 整体架构
```
Kafka消息 → StoreService → HBase存储 + Redis缓存（同步更新）
                     ↓
              船只位置管理服务 + 台风预警服务 → 实时坐标查询API
                     ↓
              Redis GEO地理索引 → 高效台风预警计算
```

### 2.2 新增模块设计
- **ShipLocationService**：船只位置管理核心服务
- **TyphoonWarningService**：台风预警服务（新增）
- **MaritimeGeoService**：海事地理计算服务（新增）
- **ShipLocationController**：坐标查询REST API接口
- **TyphoonWarningController**：台风预警API接口（新增）
- **LocationUpdateScheduler**：定时同步任务

## 3. Redis数据存储策略

### 3.1 数据结构设计

#### 3.1.1 单船只最新位置
```redis
Key: ship:location:latest:{shipSn}
Type: String (JSON)
Value: {
  "longitude": "120.123456",
  "latitude": "30.654321", 
  "timestamp": 1692345678000,
  "speed": "12.5",
  "course": "180.0",
  "updateTime": "2025-08-21 10:30:00",
  "quality": "1"
}
TTL: 24小时
```

#### 3.1.2 所有船只位置集合
```redis
Key: ship:location:all
Type: Hash
Fields: {
  "{shipSn1}": "{位置JSON}",
  "{shipSn2}": "{位置JSON}",
  ...
}
TTL: 24小时
```

#### 3.1.3 海事地理位置索引（船只+台风）
```redis
Key: geo:maritime:all
Type: GeoSet
Members: {
  # 船只位置
  member: "SHIP:{shipSn}", longitude: xxx, latitude: xxx
  # 活跃台风位置  
  member: "TYPHOON:{tfid}", longitude: xxx, latitude: xxx
}
用途: 台风预警、距离计算、范围查询
TTL: 24小时
```

#### 3.1.4 台风位置详细信息
```redis
Key: typhoon:location:latest:{tfid}
Type: String (JSON)
Value: {
  "tfid": "2025001",
  "name": "台风名称",
  "centerLat": "28.654",
  "centerLng": "125.123",
  "strong": "强台风",
  "power": "12",
  "radius7": "200",
  "radius10": "300",
  "updateTime": "2025-08-21 10:30:00"
}
TTL: 24小时
```

#### 3.1.5 台风预警缓存
```redis
Key: typhoon:warning:latest
Type: String (JSON)
Value: {
  "updateTime": "2025-08-21 10:30:00",
  "warnings": [
    {
      "shipSn": "SN001",
      "tfid": "2025001",
      "distance": 150.5,
      "warningLevel": "YELLOW",
      "typhoonName": "台风名称"
    }
  ]
}
TTL: 1小时
```

### 3.2 键值命名规范
- **船只位置**：`ship:location:*`
- **台风位置**：`typhoon:location:*`
- **地理索引**：`geo:maritime:all`
- **预警信息**：`typhoon:warning:*`

## 4. 台风预警核心功能设计

### 4.1 台风预警等级定义
```java
public enum TyphoonWarningLevel {
    GREEN("绿色", 0, 500),      // 500公里以外，安全
    BLUE("蓝色", 500, 300),     // 300-500公里，注意
    YELLOW("黄色", 300, 150),   // 150-300公里，警告
    ORANGE("橙色", 150, 50),    // 50-150公里，危险
    RED("红色", 50, 0);         // 50公里以内，极危险
}
```

### 4.2 台风预警业务流程
```
1. 台风数据更新（每小时） → 更新Redis GEO索引
2. 触发预警计算 → 使用Redis GEO范围查询
3. 计算预警等级 → 生成预警信息
4. 缓存预警结果 → 推送预警通知
5. 提供API查询 → 前端展示预警信息
```

### 4.3 Redis GEO优势分析

#### 传统方案性能问题
```java
// 需要遍历所有船只，逐一计算距离
// 时间复杂度：O(n*m)，n=船只数，m=台风数
for (Ship ship : allShips) {           // 假设100艘船
    for (Typhoon typhoon : activeTyphoons) { // 假设5个台风
        double distance = calculateDistance(...); // 500次计算
    }
}
```

#### Redis GEO方案优势
```java
// 直接查询范围内的船只
// 时间复杂度：O(log(n))，基于空间索引
List<String> affectedShips = redisTemplate.opsForGeo()
    .radius("geo:maritime:all", circle); // 1次查询
```

## 5. 核心功能实现

### 5.1 主要功能列表
1. **获取所有船只实时坐标**
2. **获取指定船只坐标**
3. **计算船只间距离**
4. **按距离范围查询附近船只**
5. **台风预警计算**（核心功能）
6. **查询台风影响范围内的船只**
7. **批量台风预警计算**
8. **台风预警等级判断**

### 5.2 API接口设计

#### 5.2.1 船只位置相关API
```http
# 获取所有船只位置
GET /api/ship/location/all

# 获取指定船只位置
GET /api/ship/location/{shipSn}

# 查询附近船只
GET /api/ship/location/nearby?longitude=120.123&latitude=30.654&radius=1000
```

#### 5.2.2 台风预警相关API
```http
# 获取台风预警信息
GET /api/typhoon/warning/latest
Response: {
  "code": 200,
  "data": {
    "updateTime": "2025-08-21 10:30:00",
    "totalWarnings": 5,
    "warnings": [
      {
        "shipSn": "SN001",
        "shipName": "船只名称",
        "tfid": "2025001",
        "typhoonName": "台风名称",
        "distance": 150.5,
        "warningLevel": "YELLOW",
        "warningMessage": "船只距离台风较近，请注意安全"
      }
    ]
  }
}

# 查询指定台风影响范围内的船只
GET /api/typhoon/{tfid}/affected-ships?radius=300
Response: {
  "code": 200,
  "data": {
    "tfid": "2025001",
    "typhoonName": "台风名称",
    "radius": 300,
    "affectedShips": [
      {
        "shipSn": "SN001",
        "distance": 150.5,
        "warningLevel": "YELLOW"
      }
    ]
  }
}

# 计算指定船只与所有台风的距离
GET /api/ship/{shipSn}/typhoon-distances
Response: {
  "code": 200,
  "data": {
    "shipSn": "SN001",
    "typhoonDistances": [
      {
        "tfid": "2025001",
        "typhoonName": "台风名称",
        "distance": 150.5,
        "warningLevel": "YELLOW"
      }
    ]
  }
}
```

## 6. 技术实现方案

### 6.1 StoreService改造

#### 6.1.1 在save2Hbase方法中添加Redis同步更新
```java
void save2Hbase(KafkaMessage message) {
    // 原有HBase存储逻辑
    save2Hbase(message, hbaseVo, cl, 100);
    
    // 新增：同步更新Redis缓存
    updateRedisCache(message, hbaseVo);
    
    // 新增：GPS数据特殊处理
    if (message.getType().equals(DeviceTypeEnum.GPS.getValue())) {
        updateShipLocationCache(message, (GpsHbaseVo) hbaseVo);
        updateMaritimeGeoIndex(message, (GpsHbaseVo) hbaseVo);
    }
}

// 新增：更新海事地理索引
private void updateMaritimeGeoIndex(KafkaMessage message, GpsHbaseVo gpsData) {
    if (isValidGpsData(gpsData)) {
        String member = "SHIP:" + message.getSn();
        Point point = new Point(Double.valueOf(gpsData.getLongitude()), 
                               Double.valueOf(gpsData.getLatitude()));
        redisTemplate.opsForGeo().add("geo:maritime:all", point, member);
    }
}
```

### 6.2 新增TyphoonWarningService

#### 6.2.1 核心方法设计
```java
@Service
public class TyphoonWarningService {
    
    // 批量计算台风预警
    public List<TyphoonWarningDto> calculateTyphoonWarnings();
    
    // 查询台风影响范围内的船只
    public List<String> getShipsInTyphoonRange(String tfid, double radiusKm);
    
    // 计算指定船只的台风预警
    public List<TyphoonWarningDto> getShipTyphoonWarnings(String shipSn);
    
    // 更新台风位置到地理索引
    public void updateTyphoonGeoIndex(String tfid, double longitude, double latitude);
    
    // 获取预警等级
    public TyphoonWarningLevel getWarningLevel(double distance);
}
```

### 6.3 台风数据同步增强

#### 6.3.1 在TyphoonService中添加地理索引更新
```java
// 在台风数据更新时同步更新地理索引
private void updateTyphoonLocationCache(TyphoonInfoVo typhoonInfo) {
    // 更新台风详细信息缓存
    String cacheKey = "typhoon:location:latest:" + typhoonInfo.getTfid();
    Map<String, Object> locationData = buildTyphoonLocationData(typhoonInfo);
    redisTemplate.opsForValue().set(cacheKey, locationData, 24, TimeUnit.HOURS);
    
    // 更新地理索引
    String member = "TYPHOON:" + typhoonInfo.getTfid();
    Point point = new Point(Double.valueOf(typhoonInfo.getCenterlng()), 
                           Double.valueOf(typhoonInfo.getCenterlat()));
    redisTemplate.opsForGeo().add("geo:maritime:all", point, member);
}
```

## 7. 数据一致性和性能优化

### 7.1 数据一致性保证

#### 7.1.1 Redis事务操作
```java
public void updateLocationAtomic(String entityId, double longitude, double latitude, 
                                String entityType, Object detailData) {
    redisTemplate.execute(new SessionCallback<Object>() {
        @Override
        public Object execute(RedisOperations operations) throws DataAccessException {
            operations.multi(); // 开启事务
            
            // 更新详细信息
            String detailKey = entityType.toLowerCase() + ":location:latest:" + entityId;
            operations.opsForValue().set(detailKey, detailData, 24, TimeUnit.HOURS);
            
            // 更新地理索引
            String member = entityType.toUpperCase() + ":" + entityId;
            operations.opsForGeo().add("geo:maritime:all", new Point(longitude, latitude), member);
            
            // 如果是船只，同时更新船只集合
            if ("SHIP".equals(entityType.toUpperCase())) {
                operations.opsForHash().put("ship:location:all", entityId, 
                                          JSONObject.toJSONString(detailData));
            }
            
            return operations.exec(); // 提交事务
        }
    });
}
```

### 7.2 性能优化策略

#### 7.2.1 批量操作优化
```java
// 批量更新多个船只位置
public void batchUpdateShipLocations(List<ShipLocationDto> locations) {
    // 使用Pipeline减少网络往返
    redisTemplate.executePipelined(new RedisCallback<Object>() {
        @Override
        public Object doInRedis(RedisConnection connection) throws DataAccessException {
            for (ShipLocationDto location : locations) {
                // 批量更新操作
                updateSingleShipLocation(connection, location);
            }
            return null;
        }
    });
}
```

#### 7.2.2 缓存预热和定时更新
```java
@Scheduled(fixedRate = 300000) // 每5分钟执行一次
public void scheduledTyphoonWarningUpdate() {
    try {
        // 1. 检查台风数据是否有更新
        if (hasTyphoonDataUpdated()) {
            // 2. 重新计算台风预警
            List<TyphoonWarningDto> warnings = calculateTyphoonWarnings();
            
            // 3. 更新预警缓存
            updateTyphoonWarningCache(warnings);
            
            // 4. 推送预警通知（如果有新的高级别预警）
            pushWarningNotifications(warnings);
        }
    } catch (Exception e) {
        logger.error("定时台风预警更新失败", e);
    }
}
```

## 8. 监控和告警

### 8.1 关键指标监控
- **数据延迟**：GPS数据到Redis缓存的延迟时间
- **缓存命中率**：Redis缓存的命中率
- **预警计算性能**：台风预警计算的响应时间
- **地理索引大小**：geo:maritime:all的成员数量

### 8.2 异常告警
- **数据同步失败**：Redis更新失败告警
- **台风预警异常**：预警计算失败告警
- **高级别预警**：红色/橙色预警自动通知

## 9. 部署配置

### 9.1 Redis配置优化
```yaml
spring:
  redis:
    host: ***************
    port: 6379
    database: 0
    timeout: 10s
    lettuce:
      pool:
        max-active: 30
        max-idle: 15
        min-idle: 5
```

### 9.2 新增配置项
```yaml
maritime:
  location:
    cache:
      ttl: 86400  # 24小时
      update-interval: 10  # 最小更新间隔（秒）
      distance-threshold: 50  # 位置变化阈值（米）
  typhoon:
    warning:
      cache-ttl: 3600  # 预警缓存1小时
      calculation-interval: 300  # 预警计算间隔5分钟
      levels:
        red: 50      # 红色预警距离（公里）
        orange: 150  # 橙色预警距离
        yellow: 300  # 黄色预警距离
        blue: 500    # 蓝色预警距离
```

## 10. 实施计划

### 10.1 开发阶段
1. **第一阶段**：StoreService改造，添加Redis同步更新和地理索引
2. **第二阶段**：TyphoonWarningService开发，实现台风预警核心功能
3. **第三阶段**：API接口开发和前端集成
4. **第四阶段**：性能优化、监控告警和测试

### 10.2 测试验证
1. **功能测试**：台风预警计算准确性验证
2. **性能测试**：大数据量下的响应时间测试
3. **压力测试**：高并发场景下的系统稳定性
4. **准确性测试**：与现有距离计算结果对比验证

---

**总结：**
本完善方案重点解决了台风预警这一核心业务场景，通过Redis GEO地理索引显著提升了地理计算性能。采用船只+台风统一地理索引的设计，实现了高效的范围查询和距离计算，为海事安全提供了强有力的技术支撑。
